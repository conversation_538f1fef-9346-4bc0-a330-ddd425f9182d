<div class="w-full h-full flex items-center justify-center" style="background: url('./img/popup-background.png'); background-size: cover; background-repeat: no-repeat; backdrop-filter: blur(.151vw);">
    <div class="w-[35.5729vw] h-[9.5833vw] flex flex-col bg-[#000000BF] rounded-[.2604vw]" v-if="$root.ShowBossPopup == 'deposit'">
        <div class="w-full h-[3.5417vw] bg-[#000000DE] rounded-t-[.2604vw] relative">
            <div class="w-[70%] h-full flex flex-col items-start justify-center left-[.5vw] absolute">
                <span class="w-full text-stone-300 text-[.8333vw] font-semibold font-['Poppins'] top-[.775vw] absolute">{{ $root.Language['deposit'] }}</span>
                <div class="w-full bottom-[.6vw] absolute">
                    <span class="text-zinc-600 text-[.6771vw] font-semibold font-['Poppins']">{{ $root.Language['deposit_description'] }}</span>
                </div>
            </div>
            <img src="./img/bossmenu/dashboard/dollar.png" class="w-[13.3333vw] h-[3.63vw] right-0 top-0 absolute">
        </div>
        <div class="w-full h-[61.5%] flex items-center justify-center relative">
            <div class="w-[98%] h-[86%] flex flex-wrap top-[.3vw] absolute">
                <div class="w-[70.5%] h-full flex flex-col">
                    <div class="w-full h-[58%] flex items-center justify-center bg-[#00F0FF] mb-[1%]">
                        <span class="w-full text-black text-[.7813vw] text-center font-semibold font-['Poppins']">{{ $root.Language['enter_an_amount'] }}</span>
                    </div>
                    <input type="number" class="w-full h-[43.5%] bg-[#000000DE] outline-none depositwithdrawinput placeholder:opacity-[.55]" placeholder="$10.000" v-model="$root.Inputs.DepositInput">
                </div>
                <div class="w-[29.5%] h-full flex flex-col items-start">
                    <div class="w-full h-[55.5%] flex items-center justify-center bg-[#000000DE] cursor-pointer" @click="$root.DepositMoney()">
                        <span class="w-full text-white text-[.7813vw] text-center font-semibold font-['Poppins']">{{ $root.Language['deposit'] }}</span>
                    </div>
                    <div class="w-full h-[2.5%]"></div>
                    <div class="w-[99%] h-[42%] flex items-center justify-center bg-[#000000DE] ml-[1%] cursor-pointer" @click="$root.ShowBossPopup = ''; $root.Inputs.DepositInput = '';">
                        <span class="w-full text-white text-[.7813vw] text-center font-semibold font-['Poppins']">{{ $root.Language['cancel'] }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="w-[35.5729vw] h-[9.5833vw] flex flex-col bg-[#000000BF] rounded-[.2604vw]" v-if="$root.ShowBossPopup == 'withdraw'">
        <div class="w-full h-[3.5417vw] bg-[#000000DE] rounded-t-[.2604vw] relative">
            <div class="w-[70%] h-full flex flex-col items-start justify-center left-[.5vw] absolute">
                <span class="w-full text-stone-300 text-[.8333vw] font-semibold font-['Poppins'] top-[.775vw] absolute">{{ $root.Language['withdraw'] }}</span>
                <div class="w-full bottom-[.6vw] absolute">
                    <span class="text-zinc-600 text-[.6771vw] font-semibold font-['Poppins']">{{ $root.Language['withdraw_description'] }}</span>
                </div>
            </div>
            <img src="./img/bossmenu/dashboard/dollar.png" class="w-[13.3333vw] h-[3.63vw] right-0 top-0 absolute">
        </div>
        <div class="w-full h-[61.5%] flex items-center justify-center relative">
            <div class="w-[98%] h-[86%] flex flex-wrap top-[.3vw] absolute">
                <div class="w-[70.5%] h-full flex flex-col">
                    <div class="w-full h-[58%] flex items-center justify-center bg-[#00F0FF] mb-[1%]">
                        <span class="w-full text-black text-[.7813vw] text-center font-semibold font-['Poppins']">{{ $root.Language['enter_an_amount'] }}</span>
                    </div>
                    <input type="number" class="w-full h-[43.5%] bg-[#000000DE] outline-none depositwithdrawinput placeholder:opacity-[.55]" placeholder="$10.000" v-model="$root.Inputs.WithdrawInput">
                </div>
                <div class="w-[29.5%] h-full flex flex-col items-start">
                    <div class="w-full h-[55.5%] flex items-center justify-center bg-[#000000DE] cursor-pointer" @click="$root.WithdrawMoney()">
                        <span class="w-full text-white text-[.7813vw] text-center font-semibold font-['Poppins']">{{ $root.Language['withdraw'] }}</span>
                    </div>
                    <div class="w-full h-[2.5%]"></div>
                    <div class="w-[99%] h-[42%] flex items-center justify-center bg-[#000000DE] ml-[1%] cursor-pointer" @click="$root.ShowBossPopup = ''; $root.Inputs.WithdrawInput = '';">
                        <span class="w-full text-white text-[.7813vw] text-center font-semibold font-['Poppins']">{{ $root.Language['cancel'] }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="w-[35.5729vw] h-[9.5833vw] flex flex-col bg-[#000000BF] rounded-[.2604vw]" v-if="$root.ShowBossPopup == 'createperm'">
        <div class="w-full h-[3.5417vw] bg-[#000000DE] rounded-t-[.2604vw] relative">
            <div class="w-[70%] h-full flex flex-col items-start justify-center left-[.5vw] absolute">
                <span class="w-full text-stone-300 text-[.8333vw] font-semibold font-['Poppins'] top-[.775vw] absolute">{{ $root.Language['create_perm'] }}</span>
                <div class="w-full bottom-[.6vw] absolute">
                    <span class="text-zinc-600 text-[.6771vw] font-semibold font-['Poppins']">{{ $root.Language['create_perm_description'] }}</span>
                </div>
            </div>
            <img src="./img/bossmenu/employee-bg.png" class="w-[13.3333vw] h-[3.63vw] right-0 top-0 absolute">
        </div>
        <div class="w-full h-[61.5%] flex items-center justify-center relative">
            <div class="w-[98%] h-[86%] flex flex-wrap top-[.3vw] absolute">
                <div class="w-[70.5%] h-full flex flex-col">
                    <div class="w-full h-[58%] mb-[1%]">
                        <input type="text" class="w-full h-full bg-[#00F0FF] px-[1w] outline-none placeholder:opacity-[.85] company-input" :placeholder="$root.Language['enter_perm_name']" v-model="$root.Inputs.PermNameInput">
                    </div>
                    <input type="text" class="w-full h-[43.5%] bg-[#000000DE] outline-none depositwithdrawinput placeholder:opacity-[.55]" :placeholder="$root.Language['enter_perm_label']" v-model="$root.Inputs.PermLabelInput">
                </div>
                <div class="w-[29.5%] h-full flex flex-col items-start">
                    <div class="w-full h-[55.5%] flex items-center justify-center bg-[#000000DE] cursor-pointer" @click="$root.CreatePerm()">
                        <span class="w-full text-white text-[.7813vw] text-center font-semibold font-['Poppins']">{{ $root.Language['create'] }}</span>
                    </div>
                    <div class="w-full h-[2.5%]"></div>
                    <div class="w-[99%] h-[42%] flex items-center justify-center bg-[#000000DE] ml-[1%] cursor-pointer" @click="$root.CloseBossPopup('createperm')">
                        <span class="w-full text-white text-[.7813vw] text-center font-semibold font-['Poppins']">{{ $root.Language['cancel'] }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="w-[35.5729vw] h-[23.75vw] flex flex-col bg-[#000000BF] rounded-[.2604vw] " v-if="$root.ShowBossPopup == 'vehicleedit' && $root.VehicleEditScreen >= 0">
        <div class="w-full h-[3.5417vw] bg-[#000000DE] rounded-[.2604vw] relative">
            <div class="w-[70%] h-full flex flex-col items-start justify-center left-[.5vw] absolute">
                <span class="w-full text-stone-300 text-[.8333vw] font-semibold font-['Poppins'] top-[.775vw] absolute">{{ $root.Language['edit_vehicle'] }}</span>
                <div class="w-full bottom-[.6vw] absolute">
                    <span class="text-zinc-600 text-[.6771vw] font-semibold font-['Poppins']">{{ $root.Language['edit_vehicle_description'] }}</span>
                </div>
            </div>
            <img src="./img/bossmenu/dashboard/dollar.png" class="w-[11.8755vw] h-full right-0 top-0 absolute">
        </div>
        <div class="w-full h-[.4vw]"></div>
        <input type="text" class="w-full h-[2.7083vw] bg-cyan-400 outline-none vehicles-screen-input" :placeholder="$root.Language['enter_vehicle_name']" v-model="$root.EditVehicleInputs.Name">
        <div class="w-full h-[.4vw]"></div>
        <input type="text" class="w-full h-[1.8229vw] bg-[#000000DE] outline-none vehicles-screen-second-input" :placeholder="$root.Language['enter_vehicle_model']" v-model="$root.EditVehicleInputs.Model">
        <div class="w-full h-[.4vw]"></div>
        <input type="text" class="w-full h-[2.7083vw] bg-cyan-400 outline-none vehicles-screen-input" :placeholder="$root.Language['enter_vehicle_img']" v-model="$root.EditVehicleInputs.Img">
        <div class="w-full h-[.4vw]"></div>
        <input type="text" class="w-full h-[1.8229vw] bg-[#000000DE] outline-none vehicles-screen-second-input" :placeholder="$root.Language['enter_vehicle_discount']" v-model="$root.EditVehicleInputs.Discount">
        <div class="w-full h-[.4vw]"></div>
        <input type="text" class="w-full h-[2.7083vw] bg-cyan-400 outline-none vehicles-screen-input" :placeholder="$root.Language['enter_vehicle_price']" v-model="$root.EditVehicleInputs.Price">
        <div class="w-full h-[.4vw]"></div>
        <div class="w-full h-[1.8229vw] flex items-center justify-center bg-[#000000DE] select-wrapper relative">
            <div class="w-full h-full flex items-center justify-center absolute">
                <select v-model="$root.SelectedVehicleEditCategory" class="top-0 absolute cursor-pointer">
                    <option class="w-full" v-for="(v, k) in $root.NewCategoryList" :value="k">{{ v.label }}</option>
                </select>
            </div>
            <span v-if="$root.SelectedVehicleEditCategory >= 0" class="text-neutral-400 text-[.7813vw] text-center font-semibold font-['Poppins'] absolute">{{ $root.NewCategoryList[$root.SelectedVehicleEditCategory].label }}</span>
            <div v-else class="w-[30%] flex items-center justify-center absolute">
                <div class="w-full flex items-center justify-center">
                    <img src="./img/bossmenu/select-icon.png" class="w-[.7813vw] h-[.7813vw]">
                    <span class="text-neutral-400 text-[.7813vw] text-center font-semibold font-['Poppins'] ml-[.5vw]">{{ $root.Language['select_category'] }}</span>
                </div>
            </div>
        </div>
        <div class="w-full h-[.8vw]"></div>
        <div class="w-full h-[2.7083vw] flex items-center justify-center">
            <div class="w-[10.3125vw] h-full flex items-center justify-center bg-[#000000DE] cursor-pointer" @click="$root.CloseEditVehicleScreen()">
                <div class="w-full text-white text-[.7813vw] text-center font-semibold font-['Poppins']">{{ $root.Language['cancel'] }}</div>
            </div>
            <div class="w-[10.3125vw] h-full flex items-center justify-center bg-[#00F0FF] cursor-pointer" @click="$root.SaveEditVehicleSection()">
                <span class="w-full text-black text-[.7813vw] text-center font-semibold font-['Poppins']">{{ $root.Language['save'] }}</span>
            </div>
        </div>
        <div class="w-full h-[.8vw]"></div>
    </div>
    <div class="w-[35.5729vw] h-[9.5833vw] flex flex-col bg-[#000000BF] rounded-[.2604vw]" v-if="$root.ShowBossPopup == 'createcategory'">
        <div class="w-full h-[3.5417vw] bg-[#000000DE] rounded-t-[.2604vw] relative">
            <div class="w-[70%] h-full flex flex-col items-start justify-center left-[.5vw] absolute">
                <span class="w-full text-stone-300 text-[.8333vw] font-semibold font-['Poppins'] top-[.775vw] absolute">{{ $root.Language['create_category'] }}</span>
                <div class="w-full bottom-[.6vw] absolute">
                    <span class="text-zinc-600 text-[.6771vw] font-semibold font-['Poppins']">{{ $root.Language['create_category_description'] }}</span>
                </div>
            </div>
            <img src="./img/bossmenu/employee-bg.png" class="w-[13.3333vw] h-[3.63vw] right-0 top-0 absolute">
        </div>
        <div class="w-full h-[61.5%] flex items-center justify-center relative">
            <div class="w-[98%] h-[86%] flex flex-wrap top-[.3vw] absolute">
                <div class="w-[70.5%] h-full flex flex-col">
                    <div class="w-full h-[58%] mb-[1%]">
                        <input type="text" class="w-full h-full bg-[#00F0FF] px-[1w] outline-none placeholder:opacity-[.85] company-input" :placeholder="$root.Language['category_name']" v-model="$root.Inputs.CategoryNameInput">
                    </div>
                    <input type="text" class="w-full h-[43.5%] bg-[#000000DE] outline-none depositwithdrawinput placeholder:opacity-[.55]" :placeholder="$root.Language['category_label']" v-model="$root.Inputs.CategoryLabelInput">
                </div>
                <div class="w-[29.5%] h-full flex flex-col items-start">
                    <div class="w-full h-[55.5%] flex items-center justify-center bg-[#000000DE] cursor-pointer" @click="$root.CreateCategory()">
                        <span class="w-full text-white text-[.7813vw] text-center font-semibold font-['Poppins']">{{ $root.Language['create'] }}</span>
                    </div>
                    <div class="w-full h-[2.5%]"></div>
                    <div class="w-[99%] h-[42%] flex items-center justify-center bg-[#000000DE] ml-[1%] cursor-pointer" @click="$root.CloseBossPopup('createcategory')">
                        <span class="w-full text-white text-[.7813vw] text-center font-semibold font-['Poppins']">{{ $root.Language['cancel'] }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="w-[35.5729vw] h-[9.5833vw] flex flex-col bg-[#000000BF] rounded-[.2604vw]" v-if="$root.ShowBossPopup == 'editcategory'">
        <div class="w-full h-[3.5417vw] bg-[#000000DE] rounded-t-[.2604vw] relative">
            <div class="w-[70%] h-full flex flex-col items-start justify-center left-[.5vw] absolute">
                <span class="w-full text-stone-300 text-[.8333vw] font-semibold font-['Poppins'] top-[.775vw] absolute">{{ $root.Language['edit_category'] }}</span>
                <div class="w-full bottom-[.6vw] absolute">
                    <span class="text-zinc-600 text-[.6771vw] font-semibold font-['Poppins']">{{ $root.Language['edit_category_description'] }}</span>
                </div>
            </div>
            <img src="./img/bossmenu/employee-bg.png" class="w-[13.3333vw] h-[3.63vw] right-0 top-0 absolute">
        </div>
        <div class="w-full h-[61.5%] flex items-center justify-center relative">
            <div class="w-[98%] h-[86%] flex flex-wrap top-[.3vw] absolute">
                <div class="w-[70.5%] h-full flex items-center justify-center">
                    <div class="w-full h-[58%] mb-[1%]">
                        <input type="text" class="w-full h-full bg-[#00F0FF] px-[1w] outline-none placeholder:opacity-[.85] company-input" :placeholder="$root.Language['category_label']" v-model="$root.Inputs.CategoryLabelInput">
                    </div>
                </div>
                <div class="w-[29.5%] h-full flex flex-col items-start">
                    <div class="w-full h-[49%] flex items-center justify-center bg-[#000000DE] cursor-pointer" @click="$root.EditCategory($root.Inputs.CategoryLabelInput)">
                        <span class="w-full text-white text-[.7813vw] text-center font-semibold font-['Poppins']">{{ $root.Language['save'] }}</span>
                    </div>
                    <div class="w-full h-[2%]"></div>
                    <div class="w-[99%] h-[49%] flex items-center justify-center bg-[#000000DE] ml-[1%] cursor-pointer" @click="$root.CloseBossPopup('editcategory')">
                        <span class="w-full text-white text-[.7813vw] text-center font-semibold font-['Poppins']">{{ $root.Language['cancel'] }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="w-[35.5729vw] h-[16.75vw] flex flex-col bg-[#000000BF] rounded-[.2604vw] " v-if="$root.ShowBossPopup == 'buyvehicle'">
        <div class="w-full h-[3.5417vw] bg-[#000000DE] rounded-[.2604vw] relative">
            <div class="w-[70%] h-full flex flex-col items-start justify-center left-[.5vw] absolute">
                <span class="w-full text-stone-300 text-[.8333vw] font-semibold font-['Poppins'] top-[.6vw] absolute">{{ $root.Language['buy_vehicle'] }}</span>
                <div class="w-full bottom-[.4vw] absolute">
                    <span class="text-zinc-600 text-[.6771vw] font-semibold font-['Poppins']">{{ $root.Language['buy_vehicle_description'] }}</span>
                </div>
            </div>
            <img src="./img/bossmenu/car-bg.png" class="w-[14.5vw] h-full right-0 bottom-0 absolute">
        </div>
        <div class="w-full h-[.4vw]"></div>
        <div class="w-full h-[2.7083vw] flex items-center justify-center bg-cyan-400">
            <span class="w-full text-[#FF0000] text-[.7813vw] text-center font-semibold font-['Poppins']">{{ $root.Language['total_price'] + ': $' + $root.FormatMoney($root.AllVehicleData[$root.SelectedBuyVehicle].price * $root.BuyVehicleInputs.Stock) }}</span>
        </div>
        <div class="w-full h-[.4vw]"></div>
        <input type="text" class="w-full h-[1.8229vw] bg-[#000000DE] outline-none vehicles-screen-second-input" :placeholder="$root.Language['enter_stock']" v-model="$root.BuyVehicleInputs.Stock">
        <div class="w-full h-[.4vw]"></div>
        <input type="text" class="w-full h-[2.7083vw] bg-cyan-400 outline-none vehicles-screen-input" :placeholder="$root.Language['enter_a_price']" v-model="$root.BuyVehicleInputs.Price">
        <div class="w-full h-[.4vw]"></div>
        <div class="w-full h-[2vw] flex items-center justify-center bg-[#000000DE] select-wrapper relative">
            <div v-if="$root.BuyVehicleInputs.SelectedCategoryIndex < 0" class="w-full flex items-center justify-center relative">
                <div class="w-full flex items-center justify-center">
                    <img src="./img/bossmenu/select-icon.png" class="w-[.7813vw] h-[.7813vw]">
                    <span class="text-neutral-400 text-[.7813vw] text-center font-semibold font-['Poppins'] ml-[.5vw]">{{ $root.Language['select_category'] }}</span>
                </div>
            </div>
            <div class="w-full h-full flex items-center justify-center absolute">
                <select v-model="$root.BuyVehicleInputs.SelectedCategoryIndex" class="top-0 absolute cursor-pointer">
                    <option class="h-full" v-for="(v, k) in $root.NewCategoryList" :value="k">{{ v.label }}</option>
                </select>
            </div>
            <span v-if="$root.BuyVehicleInputs.SelectedCategoryIndex >= 0" class="text-neutral-400 text-[.7813vw] text-center font-semibold font-['Poppins'] absolute">{{ $root.NewCategoryList[$root.BuyVehicleInputs.SelectedCategoryIndex].label }}</span>
        </div>
        <div class="w-full h-[.4vw]"></div>
        <div class="w-full h-[2.7083vw] flex items-center justify-center">
            <div class="w-[10.3125vw] h-full flex items-center justify-center bg-[#000000DE] cursor-pointer" @click="$root.CloseBuyVehicleSection()">
                <div class="w-full text-white text-[.7813vw] text-center font-semibold font-['Poppins']">{{ $root.Language['cancel'] }}</div>
            </div>
            <div class="w-[10.3125vw] h-full flex items-center justify-center bg-[#00F0FF] cursor-pointer" @click="$root.BuyVehicleSection()">
                <span class="w-full text-black text-[.7813vw] text-center font-semibold font-['Poppins']">{{ $root.Language['buy'] }}</span>
            </div>
        </div>
        <div class="w-full h-[.8vw]"></div>
    </div>
</div>