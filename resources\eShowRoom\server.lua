local showrooms = {}

RegisterServerEvent('showroom:addVehicle')
AddEventHandler('showroom:addVehicle', function(type, index, model)
    if not showrooms[type] then showrooms[type] = {} end
    showrooms[type][index] = model
    TriggerClientEvent('showroom:updateAll', -1, showrooms)
end)

RegisterServerEvent('showroom:removeVehicle')
AddEventHandler('showroom:removeVehicle', function(type, index)
    if showrooms[type] then
        showrooms[type][index] = nil
        TriggerClientEvent('showroom:updateAll', -1, showrooms)
    end
end)

RegisterNetEvent('showroom:requestData')
AddEventHandler('showroom:requestData', function()
    local src = source
    TriggerClientEvent('showroom:updateAll', src, showrooms)
end)
