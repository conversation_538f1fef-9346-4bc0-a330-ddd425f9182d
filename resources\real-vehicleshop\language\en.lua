Locales['en'] = {
    -- UI (Vehicleshop)
    ['vehicle_setup_and_information'] = "Vehicle Information & Setup",
    ['price'] = "Price",
    ['buy_this_car'] = "Buy this car",
    ['preorder_button'] = "Pre-order Now!",
    ['test_drive'] = "Test Drive",
    ['preview'] = "Preview",
    ['change_plate'] = "Change Plate",
    ['colors'] = "Colors",
    ['color_settings'] = "Color Settings",
    ['color_hex'] = "Color Hex",
    ['change_color'] = "Change Color",
    ['search'] = "Search",
    ['type'] = "Type",
    ['car_list'] = "Car List",
    ['stock'] = "Stock",
    ['kits'] = "Kits",
    ['top_speed'] = "Top Speed",
    ['braking'] = "Braking",
    ['acceleration'] = "Acceleration",
    ['suspension'] = "Suspension",
    ['handling'] = "Handling",
    ['exit_preview'] = "Exit Preview",
    ['inspect_exterior'] = "Inspect Exterior",
    ['inspect_interior'] = "Inspect Interior",
    ['preview_mode_information_text'] = "Rotate the car for better view!",
    ['are_you_sure'] = "Are You Sure?",
    ['leave_us_a_feedback'] = "Leave Us a Feedback!",
    ['feedback_description'] = "Thanks for choosing us. If you want to leave a feedback we would love to!",
    ['confirm'] = "Confirm",
    ['cancel'] = "Cancel",
    ['close'] = "Close",
    ['words'] = "Words",
    ['complaint_header'] = "Let us know your complaint!",
    ['complaint_description'] = "Let us know your complaint so we can fix ourselves.",
    ['testdrive_header'] = "You have to pay for a test drive",
    ['testdrive_description'] = "A test drive is there for you to get to know the vehicle better, but you have to pay a certain amount for it. But we are sure you will not regret it. All customers usually do a test drive, before they buy.",
    ['not_enough_money'] = "You don't have enough money.",
    ['buyvehicle_header'] = "Check the vehicle's details before buying",
    ['buyvehicle_description'] = "You can test drive the vehicle before you buy it, you can also change the color of the vehicle and if you are not happy with the license plate, you can change it by paying a certain fee.",
    ['plate_already_exist'] = "License plates are already in use.",
    ['preordervehicle_header'] = "You are pre-ordering the car",
    ['preordervehicle_description'] = "You can test drive the vehicle before you pre-order it, you can also choose the color of the vehicle. You will pay directly, and if not accepted, a refund will be given.",

    -- UI (Boss menu)
    ['vehicle_stock_list'] = "Stock of vehicles in your company",
    ['company_money'] = "Company Money",
    ['available'] = "Available",
    ['menu'] = "Menu",
    ['dashboard'] = "Dashboard",
    ['number_of_employeer'] = "Number Of Employees",
    ['employee'] = "Employee",
    ['vehicles_in_stock'] = "Vehicles In Stock",
    ['vehicle'] = "Vehicle",
    ['vehicles_sold'] = "Number of Vehicles Sold",
    ['company_rating'] = "Company rating",
    ['average'] = "Average",
    ['bad'] = "Bad",
    ['good'] = "Good",
    ['preorder'] = "Pre-order",
    ['preorder_description'] = "If you accept, the vehicle will be automatically sent to the customer.",
    ['ordered_by'] = "Ordered By",
    ['ordered_model'] = "Ordered Model",
    ['decline'] = "Decline",
    ['accept'] = "Accept",
    ['preorder_second_description'] = "You can change the page by right/left clicking.",
    ['sold_vehicles'] = "Sold Vehicles",
    ['sold_vehicles_description'] = "List of sold vehicles in your company.",
    ['buyer'] = "Buyer",
    ['purchased_model'] = "Purchased Model",
    ['date'] = "Date",
    ['company_name'] = "Company Name",
    ['current_company_name'] = "Current Company Name",
    ['save'] = "Save",
    ['trasnfer_company'] = "Transfer Company",
    ['trasnfer_company_description'] = "You can transfer your company to someone else by writing ID.",
    ['transfer'] = "Transfer",
    ['make_bulk_discount'] = "Make Bulk Discount",
    ['make_bulk_discount_description'] = "Give a discount on all products by entering percent.",
    ['discount_description'] = "Please enter % flood value for All Vehicles.",
    ['cancel_discount'] = "Cancel Discount",
    ['delete_all_logs'] = "Delete All Logs",
    ['delete_all_logs_description'] = "Sometimes clear logs to avoid database bloat.",
    ['delete_all_logs_information_text'] = "This action deletes all messages and log records.",
    ['delete'] = "Delete",
    ['bonuses_header'] = "Distribute bonuses to employees",
    ['bonuses_description'] = "This action sends bonuses to all staff.",
    ['bonuses_information'] = "Enter the amount of bonus to be sent to All Employees.",
    ['send'] = "Send",
    ['raise_the_price'] = "Raise the price",
    ['raise_description'] = "You can raise prices on all products.",
    ['raise_information'] = "Enter the percentage increase below.",
    ['cancel_raise'] = "Cancel The Raise",
    ['company_balance'] = "Company Balance",
    ['company_money_description'] = "Here you can see your company's balance.",
    ['deposit'] = "Deposit",
    ['withdraw'] = "Withdraw",
    ['profit'] = "Profit",
    ['profit_description'] = "Here you can see the company's earnings so far.",
    ['profit_information'] = "If the logs are deleted, the profit here cannot be calculated!",
    ['earned'] = "Earned",
    ['payout'] = "Payout",
    ['payout_description'] = "Here are the company's expenditures to date.",
    ['payout_information'] = "If the logs are deleted, the expenses here cannot be calculated!",
    ['was_spent'] = "Was Spent",
    ['deposit_withdraw_transaction'] = "Deposit and Withdrawal Transactions",
    ['dwt_description'] = "The company's withdrawal and deposit history.",
    ['staff_settings_header'] = "Employee settings",
    ['staff_settings_description'] = "Here you can edit the rank and salary of employees.",
    ['staff'] = "Staff",
    ['rank'] = "Rank",
    ['amount'] = "Amount",
    ['withdrawn'] = "withdrawn",
    ['deposited'] = "Deposited",
    ['recruit_staff'] = "Recruit Staff",
    ['recruit_staff_description'] = "Here you can hire workers for your company and determine their salary.",
    ['send_request'] = "Send Request",
    ['salary_penalty'] = "Salary Penalty",
    ['salary_penalty_description'] = "You can impose a salary penalty on those who work here.",
    ['punish'] = "Punish",
    ['list_of_personnel_with_salary_penalty'] = "List of Personnel with Salary Penalty",
    ['lopwsp_description'] = "List of Personnel Who Received Salary Penalty.",
    ['salary'] = "Salary",
    ['end_the_punishment'] = "End The Punishment",
    ['staff_list'] = "Staff List",
    ['staff_list_description'] = "Information about the staff team.",
    ['rank_up'] = "Rank Up",
    ['reduce_rank'] = "Reduce Rank",
    ['fire'] = "Fire",
    ['deposit_description'] = "Put money into your company.",
    ['withdraw_description'] = "Withdraw money from your company.",
    ['enter_an_amount'] = "Enter an Amount",
    ['want_to_buy_the_company'] = "Do you want to buy this company?",
    ['wtbtc_description'] = "This action cannot be reversed, check that the store name is correct.",
    ['job_req'] = "You've been offered a job. Do you accept?",
    ['job_req_description'] = "Before accepting, check the name of the company and your salary.",
    ['reject'] = "Reject",
    ['day'] = "Day",
    ['staff_settings'] = "Staff Settings",
    ['company_settings'] = "Company Settings",
    ['perms'] = "Perms",
    ['perms_description'] = "You can create/change perms.",
    ['create'] = "Create",
    ['remove'] = "Remove",
    ['edit'] = "Edit",
    ['create_perm'] = "Create Perms",
    ['create_perm_description'] = "You can create authorization in this section.",
    ['feedbacksandcomplaints'] = "Feedback & Complaints",
    ['feedbacksandcomplaints_description'] = "Read feedbacks and complaints from customers.",
    ['read'] = "Read",
    ['feedback'] = "Feedback",
    ['complaint'] = "Complaint",
    ['buy_vehicle'] = "Buy Vehicle",
    ['select_category'] = "Select Category",
    ['edit_vehicle'] = "Edit Selected Vehicle",
    ['edit_vehicle_description'] = "In this section you can edit the vehicle information.",
    ['category'] = "Category",
    ['category_description'] = "You can create and edit categories in this section.",
    ['show'] = "Show",
    ['show_category_vehicles'] = "You can see the vehicles in the category of your choice.",
    ['create_category'] = "Create Category",
    ['create_category_description'] = "You can create category in this section.",
    ['edit_category'] = "Edit Category",
    ['edit_category_description'] = "Edit selected category in this section.",
    ['buy'] = "Buy",
    ['buy_vehicle_description'] = "Buy vehicle to your company in this section.",
    ['total_price'] = "Total Price",
    ['time_left'] = "Time Left",
    ['no_data'] = "There is no available data.",

    -- UI Inputs (Placeholders)
    ['feedback_input_placeholder'] = "Min 50 characters & Max 150 characters.",
    ['complaint_input_placeholder'] = "Min 50 characters & Max 150 characters.",
    ['search_input_placeholder'] = "Name, Label, Model Search...",
    ['bossmenu_search_input'] = "Search...",
    ['enter_player_id'] = "Enter Player ID...",
    ['enter_sale_price'] = "Enter Sale Price...",
    ['write_percent_value'] = "Write %",
    ['enter_a_price'] = "Enter a Price...",
    ['enter_the_salary'] = "Enter The Salary...",
    ['penalty_time'] = "How Many Salary Penalties?",
    ['enter_perm_name'] = "Enter Perm Name...",
    ['enter_perm_label'] = "Enter Perm Label...",
    ['enter_vehicle_name'] = "Enter Vehicle Name...",
    ['enter_vehicle_model'] = "Enter Vehicle Model...",
    ['enter_vehicle_img'] = "Enter Vehicle IMG (URL)...",
    ['enter_vehicle_discount'] = "Enter Vehicle Discount (%)...",
    ['enter_vehicle_price'] = "Enter Vehicle Price...",
    ['category_name'] = "Enter Category Name...",
    ['category_label'] = "Enter Category Label...",
    ['enter_stock'] = "Enter stock...",

    -- UI Notify
    ['successful'] = "Successful",
    ['information'] = "Information",
    ['error'] = "Error",
    ['choose_point'] = "First choose how many points you want to give to this company!",
    ['feedback_stop_using_bad_words'] = "If you want to give feedback, do it properly, without bad words. Be human!",
    ['feedback_minimum_character'] = "You have to write at least 50 words!",
    ['feedback_maximum_character'] = "You can't write more than 150 words!",
    ['complaint_stop_using_bad_words'] = "If you want to complain, do it properly, without bad words. Be human!",
    ['complaint_minimum_character'] = "You have to write at least 50 words!",
    ['complaint_maximum_character'] = "You can't write more than 150 words!",
    ['too_long_plate'] = "The license plate must not exceed 6 characters!",
    ['successfully_changed_plate'] = "Successfylly changed plate!",
    ['new_generated_plate'] = "The special license plate was deleted, a new one was created and the price was reduced.",
    ['dont_leave_empty'] = "Do not leave the field empty!",
    ['not_allowed'] = "You are not authorized to take this action",
    ['not_enough_money_in_company'] = "Your company doesn't have that much money.",
    ['successfully_deposited'] = "You have successfully deposited money.",
    ['successfully_withdrawn'] = "You have successfully withdrawn money.",
    ['successfully_changed_company_name'] = "You successfully changed the name of the company.",
    ['successfully_launched_discount'] = "Successfully launched a discount campaign.",
    ['successfully_canceled_discount'] = "The discount campaign is over.",
    ['successfully_deleted_logs'] = "All logs were successfully deleted.",
    ['successfully_sent_bonus'] = "Successfully distributed bonuses to all employees",
    ['not_enough_employee_for_bonus'] = "There is only one employee and he is the owner of the company. You can't give a bonus to the owner of the company.",
    ['successfully_raised'] = "You have successfully raised prices on all products.",
    ['targetplayer_does_not_have_money'] = "The other player doesn't have the money to accept your offer",
    ['targetplayer_rejected'] = "The player rejected your offer.",
    ['not_enough_perms'] = "There is no authorization to be given to the player. Create first.",
    ['accepted_job_offer'] = "The person has accepted your job offer.",
    ['salary_penalty_applied'] = "Salary penalty applied.",
    ['removed_penalty'] = "The person's sentence was terminated.",
    ['no_higher_rank'] = "There is no higher rank to be given. There is no other higher rank other than Owner. Create one.",
    ['no_lower_rank'] = "There is no lower rank to be given.",
    ['rank_changed'] = "The rank was successfully changed.",
    ['fired_employee'] = "The employee was fired from the company.",
    ['cant_fine_owner'] = "You can't fine the owner of the company.",
    ['category_name_exist'] = "There is already a category with this name.",
    ['category_label_exist'] = "There is already a category with this label.",
    ['category_created'] = "Category created.",
    ['category_removed'] = "Category removed.",
    ['category_updated'] = "Category updated.",
    ['vehicle_edit_no_change'] = "No changes were made to the vehicle information.",
    ['vehicle_updated'] = "The vehicle's information has been updated.",
    ['stock_updated'] = "Stock of the vehicle has been updated.",
    ['bought_vehicle'] = "You have successfully purchased the vehicle. Congratulations.",
    ['perm_name_exist'] = "There is already a perm with this name.",
    ['perm_label_exist'] = "There is already a perm with this label.",
    ['created_perm'] = "Successfully created perm.",
    ['perm_deleted'] = "Perm was successfully deleted.",
    ['no_change'] = "No changes have been made.",
    ['perm_settings_saved'] = "Perm settings saved.",
    ['complaint_deleted'] = "Complaint successfully deleted.",
    ['feedback_deleted'] = "Feedback successfully deleted.",
    ['already_has_a_discount'] = "The company already has a discount campaign.",
    ['no_discount_campaign'] = "There is no discount campaign available.",
    ['same_discount'] = "You cannot rewrite the current discount %.",
    ['preorder_request_sent'] = "Request Sent.",
    ['preorder_declined'] = "Pre-order denied.",
    ['preorder_accepted'] = "Pre-order accepted",
    ['couldnt_remove_perm'] = "There is a player with the perm you are trying to delete. That's why the perm could not be deleted.",

    -- Drawtext
    ['cancel_testdrive'] = "~INPUT_SCRIPTED_FLY_ZDOWN~ - Cancel Test Drive",
    ['bossmenu_marker'] = "~INPUT_PICKUP~ - Open Bossmenu",
    ['buy_company_marker'] = "~INPUT_PICKUP~ - Buy This Company",
    ['complaint_form'] = "~INPUT_PICKUP~ - Send Complaint Form",

    -- Notify
    ['not_allowed_to_open_vs'] = "You don't have permission to access to this shop.",
    ['successfully_bought_company'] = "Congratulations! This company belongs to you now.",
    ['request_sent'] = "Request sent to the target player.",
    ['player_not_found'] = "There is no such player on the server.",
    ['same_player_error'] = "You can't send requests to yourself.",
    ['sold_company'] = "You successfully sold the company.",
    ['got_job'] = "You got a job. Congratulations.",
    ['rejected_job_offer'] = "The person rejected your job offer.",
    ['same_player_error_second'] = "You can't enter your own id.",
    ['complaint_sent'] = "Your complaint form has been successfully submitted.",
    ['feedback_sent'] = "Sent feedback.",

    -- Mail
    ['salary_subject'] = 'Your salary has been paid!',
    ['salary_message'] = "Your salary was paid by the company you work for. You can go to the bank app and check the incoming price.",
    ['fired_subject'] = "You're fired!",
    ['fired_message'] = "I'm afraid you've been fired from the company you work for. You can contact the manager or owner of the company for more information.",
    ['reduce_rank_subject'] = "Your authority has been reduced!",
    ['reduce_rank_message'] = "Unfortunately, you have been demoted for your bad deeds and low activity. For more information, you can contact the company officials.",
    ['rankup_subject'] = "You have risen in rank!",
    ['rankup_message'] = "Congratulations! We've rewarded you for the work you've done for this company so far. We've elevated your authority. Keep up the good work...",
    ['punishmentend_subject'] = "Your sentence is terminated!",
    ['punishmentend_message'] = "Your punishment is over. Don't make the same mistake next time!",
    ['penalty_subject'] = "You've been punished!",
    ['penalty_message'] = "You were given a salary penalty for your mistakes. I hope this will prevent you from making mistakes again.",
    ['bonus_subject'] = "Employees received bonuses!",
    ['bonus_message'] = "Thanks to your efficiency and willingness to work, the company is growing day by day. For this, we have distributed an award to all of you.",
    ['not_enough_salary_subject'] = "Your salary has not been paid!",
    ['not_enough_salary_message'] = "Unfortunately, there is not enough money in the company's coffers to pay the employees. That's why your salary could not be paid this month.",
    ['preorder_rejected_subject'] = "Your Pre-Order has been rejected!",
    ['preorder_rejected_message'] = "Hello, since there was no response to your pre-order and 24 hours have passed, it was automatically rejected by the system and your money was refunded. We wish you a good day...",
    ['preorder_declined_subject'] = "Your Pre-Order has been denied!",
    ['preorder_declined_message'] = "Hello, your pre-order was declined by the company and your money was refunded. You can check your bank. We wish you a good day...",
    ['preorder_accepted_subject'] = "Your pre-order has been accepted!",
    ['preorder_accepted_message'] = "Congratulations on your new vehicle. The company has accepted your pre-order and the vehicle has been sent to your garage. Have a safe drive. Vehicle plate:",

    -- Permissions
    ['administration'] = 'Administration',
    ['administration_description'] = 'The player can, for example, change the company name and other management actions like perm create.',
    ['withdraw_deposit'] = 'Withdraw & Deposit',
    ['withdraw_deposit_description'] = 'Player can withdraw and deposit money.',
    ['preorder_perm'] = 'Preorder',
    ['preorder_description_perm'] = 'Player can accept/reject preorder request.',
    ['discount'] = 'Discount',
    ['discount_description_perm'] = 'Player can start discount campaign.',
    ['remove_log'] = 'Remove Log',
    ['remove_log_description'] = 'Player can remove all log data.',
    ['bonus'] = 'Bonus',
    ['bonus_description'] = 'Player can give bonus to other staff members.',
    ['raise'] = 'Raise',
    ['raise_description_perm'] = 'Player can bring a raise.',
    ['fire_employees'] = 'Fire Employees',
    ['fire_employees_description'] = 'Player can fire staff members.',
    ['edit_staff_rank'] = 'Edit Staff Rank',
    ['edit_staff_rank_description'] = 'Player can demote and promote employees.',
    ['hire_staff'] = 'Hire Staff',
    ['hire_staff_description'] = 'Player can hire staff members.',
    ['give_penalty'] = 'Give Penalty',
    ['give_penalty_description'] = 'Player can give penalty to other staff members.',
    ['edit_remove_add_category'] = 'Edit/Remove/Add Category',
    ['edit_remove_add_category_description'] = 'Player can add, remove and edit categories.',
    ['buy_vehicle_stock'] = 'Buy Vehicle Stock',
    ['buy_vehicle_stock_description'] = 'Player can buy vehicle stock.',
    ['edit_vehicles'] = 'Edit Vehicles',
    ['edit_vehicles_description'] = 'Player can edit vehicle category, price, give discount etc.',
    ['remove_feedbacks'] = 'Remove Feedbacks',
    ['remove_feedbacks_description'] = 'Player can remove feedbacks.',
    ['remove_complaints'] = 'Remove Complaints',
    ['remove_complaints_description'] = 'Player can remove complaints.'
}