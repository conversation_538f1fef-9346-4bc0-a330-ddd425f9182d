<div class="w-full h-full flex items-center justify-between">
    <div class="w-full h-[98%] flex flex-col bg-[#000000BF] rounded-[.2604vw]">
        <div class="w-full h-[12.3%] bg-[#000000DE] rounded-t-[.2604vw] relative">
            <img src="./img/bossmenu/second-car-bg.png" class="w-[14.5vw] h-[3.85vw] right-0 bottom-0 absolute">
            <div class="w-[73%] h-full flex flex-col items-start justify-center left-[1.5%] top-0 absolute">
                <span class="w-full text-[#C3C3C3] text-[.8333vw] font-semibold font-['Poppins'] mt-[.2vw]">{{ $root.Language['buy_vehicle'] }}</span>
                <span class="w-full text-zinc-600 text-[.6771vw] font-semibold font-['Poppins']">{{ $root.Language['buy_vehicle_description'] }}</span>
            </div>
        </div>
        <div class="w-full h-[2%]"></div>
        <div class="w-full h-[86%] flex items-start justify-center">
            <div class="w-[98%] h-[99%] block overflow-y-auto overflow-x-hidden">
                <div v-for="(v, k) in $root.AllVehicleData" class="w-[19.5%] h-[39.3%] flex flex-col float-left mr-[.2vw] mb-[.3vw] hover:scale-[.98]" :class="[(k + 1) % 5 === 0 ? 'no-margin' : '']">
                    <div class="w-full h-[79%] bg-[#000000DE] rounded-[.2604vw] relative">
                        <img src="./img/bossmenu/vehicle-container-effect-gray.png" class="w-[14.5833vw] h-[9.1146vw] right-0 absolute">
                        <div class="w-full h-full flex flex-wrap items-center left-0 top-0 absolute">
                            <div class="w-[4%] h-full"></div>
                            <div class="w-[80%] h-[86%] flex flex-col items-start justify-start relative">
                                <span class="w-full text-white text-[.8333vw] font-semibold font-['Poppins'] left-0 top-0 absolute">{{ v.label }}</span>
                                <span class="w-full text-neutral-400 text-[.6771vw] font-semibold font-['regular'] left-0 top-[1vw] absolute">{{ v.model }}</span>
                            </div>
                            <div class="w-[16%] h-full"></div>
                        </div>
                        <div class="w-[70%] h-full flex items-center justify-center right-0 top-0 absolute">
                            <img :src="v.img" class="w-full h-auto">
                        </div>
                        <div class="w-[26%] h-[13%] flex items-center justify-center bg-[#00F0FF] rounded-[.1vw] left-[.45vw] bottom-[1vw] absolute cursor-pointer" @click="$root.OpenBuyVehicleScreen(k)">
                            <span class="w-full text-black text-[.5208vw] text-center font-semibold font-['Poppins']">{{ $root.Language['buy'] }}</span>
                        </div>
                    </div>
                    <div class="w-full h-[2%]"></div>
                    <div class="w-full h-[18%] flex items-center justify-start bg-[#000000DE] rounded-[.2604vw]">
                        <span class="w-full text-white text-[.7292vw] font-semibold font-['Poppins'] pl-[.5vw]">{{ $root.Language['price'] + ': $' + $root.FormatMoney(v.price) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>