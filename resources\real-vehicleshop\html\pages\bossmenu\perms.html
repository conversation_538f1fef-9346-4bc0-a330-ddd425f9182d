<div class="w-full h-full flex items-center justify-between">
    <div class="w-[34.3%] h-[98%] flex flex-col bg-[#000000BF] rounded-[.2604vw]">
        <div class="w-full h-[12%] bg-[#000000DE] rounded-t-[.2604vw] relative">
            <img src="./img/bossmenu/bg-effect.png" class="w-[12.5vw] h-full right-0 absolute">
            <div class="w-[4.4792vw] h-[1.1458vw] flex items-center justify-center bg-[#00F0FF] rounded-[.17vw] right-[1vw] top-[1.2vw] absolute cursor-pointer" @click="$root.PermCheck($root.PlayerRank, 'administration') && ($root.ShowBossPopup = 'createperm')">
                <span class="w-full text-black text-[.5208vw] text-center font-semibold font-['Poppins']">{{ $root.Language['create'] }}</span>
            </div>
            <div class="w-[73%] h-full flex flex-col items-start justify-center left-[1.5%] top-0 absolute">
                <span class="w-full text-[#C3C3C3] text-[.8333vw] font-semibold font-['Poppins'] mt-[.2vw]">{{ $root.Language['perms'] }}</span>
                <span class="w-full text-zinc-600 text-[.6771vw] font-semibold font-['Poppins']">{{ $root.Language['perms_description'] }}</span>
            </div>
        </div>
        <div class="w-full h-[2%]"></div>
        <div class="w-full h-[84%] flex items-center justify-center">
            <div class="w-[97%] h-full block overflow-y-auto overflow-x-hidden">
                <div v-for="(v, k) in $root.PermsTable" class="w-[98.5%] h-[9%] bg-[#000000DE] rounded-[.17vw] flex flex-wrap mb-[.15vw]">
                    <div class="w-[2%] h-full"></div>
                    <div class="w-[50%] h-full flex items-center justify-center">
                        <span class="w-full text-white text-[.7292vw] font-medium font-['Poppins']">{{ v.label }}</span>
                    </div>
                    <div class="w-[5%] h-full"></div>
                    <div class="w-[40.5%] h-full flex items-center justify-between">
                        <div class="w-[48%] h-[55%] flex items-center justify-center bg-[#5D5D5D] rounded-[.17vw] cursor-pointer" v-if="v.removable" @click="$root.RemovePerm(k)">
                            <div class="w-full text-white text-[.5208vw] text-center font-semibold font-['Poppins']">{{ $root.Language['remove'] }}</div>
                        </div>
                        <div class="w-[48%] h-[55%] flex items-center justify-center bg-[#00F0FF] rounded-[.17vw] cursor-pointer" v-if="v.editable" @click="$root.ResetPermsToggle(); $root.SelectedPerm = k;">
                            <div class="w-full text-black text-[.5208vw] text-center font-semibold font-['Poppins']">{{ $root.Language['edit'] }}</div>
                        </div>
                    </div>
                    <div class="w-[2.5%] h-full"></div>
                </div>
            </div>
        </div>
        <div class="w-full h-[2%]"></div>
    </div>
    <div class="w-[65.3%] h-[98%] flex flex-col bg-[#000000BF] rounded-[.2604vw]" v-if="$root.SelectedPerm >= 0">
        <div class="w-full h-[10%] bg-[#000000DE] rounded-t-[.2604vw] relative">
            <img src="./img/bossmenu/employee-bg.png" class="w-[13.3333vw] h-[3.85vw] right-0 bottom-0 absolute">
            <div class="w-[73%] h-full flex flex-col items-start justify-center left-[1.5%] top-0 absolute">
                <span class="w-full text-[#C3C3C3] text-[.8333vw] font-semibold font-['Poppins'] mt-[.2vw]">{{ $root.Language['perms'] }}</span>
                <span class="w-full text-zinc-600 text-[.6771vw] font-semibold font-['Poppins']">{{ $root.Language['perms_description'] }}</span>
            </div>
        </div>
        <div class="w-full h-[2%]"></div>
        <div class="w-full h-[88%] flex items-start justify-center">
            <div class="w-[96.7%] h-[94%] flex flex-col">
                <div class="w-full h-[91%] block overflow-y-auto overflow-x-hidden">
                    <div v-for="(v, k) in $root.PermsTable[$root.SelectedPerm].permissions" class="w-[49.2%] h-[18.5%] flex flex-wrap bg-[#020202] rounded-[.4vw] float-left mb-[.4vw] permission-container">
                        <div class="w-[3.8%] h-full"></div>
                        <div class="w-[12.6%] h-full flex items-center justify-center">
                            <div class="w-full h-[66%] flex items-center justify-center bg-[#2B2A2A] rounded-[.1563vw]">
                                <inlinesvg src="./img/bossmenu/permissiong-icon.svg"></inlinesvg>
                            </div>
                        </div>
                        <div class="w-[3%] h-full"></div>
                        <div class="w-[63%] h-full flex flex-col items-start justify-center">
                            <span class="w-full text-white text-[.7292vw] font-medium font-['Poppins']">{{ v.label }}</span>
                            <span class="w-full text-zinc-500 text-[.5208vw] font-normal font-['Poppins'] pt-[.2vw]">{{ v.description }}</span>
                        </div>
                        <div class="w-[1%] h-full"></div>
                        <div class="w-[13.5%] h-full flex items-center justify-center relative">
                            <div class="w-full h-[1.5104vw] flex items-center bg-[#4B4B4B] rounded-[2vw] relative cursor-pointer toggle-perm" :class="{ 'active': v.value }" @click="$root.TogglePerms(k)">
                                <div class="w-[1.0938vw] h-[1.0938vw] rounded-[50%] left-[.2083vw] absolute toggle-circle" :class="{ 'bg-[#00F0FF]': v.value, 'bg-white': !v.value }"></div>
                            </div>
                            <input type="checkbox" class="w-full h-[1.5104vw] rounded-[2vw] absolute hidden" :checked="v.value">
                        </div>
                        <div class="w-[3.1%] h-full"></div>
                    </div>
                </div>
                <div class="w-full h-[4%]"></div>
                <div class="w-full h-[8%] flex justify-start">
                    <div class="w-[18.5%] h-full flex items-center justify-center bg-[#00F0FF] rounded-[.2083vw] cursor-pointer" @click="$root.SaveNewPermissions()">
                        <span class="w-full text-black text-[.7292vw] text-center font-semibold font-['Poppins']">{{ $root.Language['save'] }}</span>
                    </div>
                    <div class="w-[81.5%] h-full"></div>
                </div>
            </div>
        </div>
    </div>
</div>