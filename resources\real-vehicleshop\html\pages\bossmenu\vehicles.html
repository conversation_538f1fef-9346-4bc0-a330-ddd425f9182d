<div class="w-full h-full block overflow-y-auto overflow-x-hidden">
    <div v-for="(v, k) in $root.BossMenuFilterVehicles" class="w-[24.3%] h-[39.3%] flex flex-col float-left mr-[.673vw] mb-[.673vw] hover:scale-[.98]" :class="[(k + 1) % 4 === 0 ? 'no-margin' : '']">
        <div class="w-full h-[79%] bg-[#000000DE] rounded-[.2604vw] relative">
            <img src="./img/bossmenu/vehicle-container-effect-gray.png" class="w-[14.5833vw] h-[9.1146vw] right-0 absolute">
            <div class="w-full h-full flex flex-wrap items-center left-0 top-0 absolute">
                <div class="w-[4%] h-full"></div>
                <div class="w-[80%] h-[86%] flex flex-col items-start justify-start relative">
                    <span class="w-full text-white text-[.8333vw] font-semibold font-['Poppins'] left-0 top-0 absolute">{{ v.label }}</span>
                    <span class="w-full text-neutral-400 text-[.6771vw] font-semibold font-['regular'] left-0 top-[1vw] absolute">{{ v.model }}</span>
                </div>
                <div class="w-[16%] h-full"></div>
            </div>
            <div class="w-[70%] h-full flex items-center justify-center right-0 top-0 absolute">
                <img :src="v.img" class="w-full h-auto">
            </div>
            <div class="w-[26%] h-[13%] flex items-center justify-center bg-[#00F0FF] rounded-[.1vw] left-[.45vw] bottom-[1vw] absolute cursor-pointer" @click="$root.OpenEditVehicleScreen(k, v.label, v.model, v.img, v.discount, v.price, v.category)">
                <span class="w-full text-black text-[.5208vw] text-center font-semibold font-['Poppins']">{{ $root.Language['edit'] }}</span>
            </div>
        </div>
        <div class="w-full h-[3%]"></div>
        <div class="w-full h-[17%] flex items-center justify-between bg-[#000000DE] rounded-[.2604vw]">
            <span class="w-[49%] text-white text-[.8333vw] font-semibold font-['Poppins'] pl-[.5vw]">{{ $root.Language['stock'] + ': ' + v.stock }}</span>
            <span class="w-[49%] text-neutral-400 text-[.8333vw] text-right font-semibold font-['Poppins'] pr-[.5vw]">${{ $root.FormatMoney(v.price) }}</span>
        </div>
    </div>
</div>